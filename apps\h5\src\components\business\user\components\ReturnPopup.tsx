'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  OrderDisabledReturnProducts,
  OrderReturnFormatItem,
  OrderReturnItem,
  OrderReturnProducts,
  PriceRange,
  PriceRanges,
  ROUTE,
  setReturnAddressId,
  setSelectedProducts,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Image, Stepper } from 'antd-mobile'

import { CustomButton, CustomCheckbox, CustomPopup } from '@/components'
import { commonStyles } from '@/constants'

type ReturnPopupProps = {
  orderNumber: string
  products: OrderReturnProducts
  disabledProducts: OrderDisabledReturnProducts
  addressId: string
  popupVisible: boolean
  closePopup?: () => void
  from?: string
}

/**
 * 退货弹窗
 */
const ReturnPopup = (props: ReturnPopupProps) => {
  const { orderNumber, products, disabledProducts, addressId, popupVisible, closePopup, from } =
    props

  const getI18nString = useTranslations('Common')
  const { openPage } = useNavigate()

  const dispatch = useAppDispatch()

  const [formatProducts, setFormatProducts] = useState<OrderReturnItem[]>([])

  const isAllChecked = useMemo(() => {
    return formatProducts.every((product) => product.check)
  }, [formatProducts])

  /**
   * 是否只有一个可选产品
   */
  const isOnlyOneProduct = useMemo(() => {
    return products?.length === 1
  }, [products])

  const checkParams = useMemo(() => {
    return formatProducts.reduce((pre: OrderReturnFormatItem[], item) => {
      if (item.check || isOnlyOneProduct) {
        pre.push({
          item_id: item.id,
          qty: Number(item.qty),
        })
      }
      return pre
    }, [])
  }, [formatProducts, isOnlyOneProduct])

  const sectionListData = useMemo(() => {
    const data = [
      {
        title:
          formatProducts?.length === 1
            ? getI18nString('section_can_return_only_qty')
            : getI18nString('section_can_return'),
        // subTitle: getI18nString('section_can_return_tip'),
        subTitle: '',
        data: formatProducts,
        canReturn: true,
      },
    ]
    if (Number(disabledProducts?.length) > 0) {
      data.push({
        title: getI18nString('section_cant_return'),
        subTitle: '',
        data: disabledProducts as OrderReturnItem[],
        canReturn: false,
      })
    }
    return data
  }, [formatProducts, disabledProducts, getI18nString])

  const renderReturnItem = useCallback(
    (
      canReturn: boolean,
      productInfo: OrderReturnItem,
      handleQtyChange: (id: string, value: number) => void,
    ) => {
      const { id, product, ordered, qty, status_label } = productInfo

      /**
       * 是否是 Configurable 商品
       */
      const isConfigurableProduct = Number(productInfo?.config_selected_options?.length) > 0

      /**
       * configurable 商品的 option value
       */
      const configurableProductOptionValue = isConfigurableProduct
        ? productInfo?.config_selected_options?.map((option) => option?.value_label)
        : []

      if (!product) {
        return
      }

      return (
        <div className="flex w-full flex-row">
          {canReturn ? (
            <div className="h-[88px] w-[88px]">
              <Image
                style={{
                  width: 88,
                  height: 88,
                  backgroundColor: '#F8F8F9',
                  borderRadius: 8,
                }}
                src={product?.image?.url || ''}
                fit="cover"
                alt={product?.image?.label || ''}
              />
            </div>
          ) : (
            <div className="relative h-[88px] w-[88px]">
              <Image
                style={{
                  width: 88,
                  height: 88,
                  backgroundColor: '#F8F8F9',
                  borderRadius: 8,
                }}
                src={product?.image?.url || ''}
                fit="cover"
                alt={product?.image?.label || ''}
              />
              <div className="absolute bottom-0 flex h-8 w-full flex-row items-center justify-center rounded-b-[8px] bg-[#FEE5E5]">
                <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#DA291C]">
                  {status_label}
                </div>
              </div>
            </div>
          )}

          <div className="w-full pl-[12px]">
            <div className="mb-[8px]">
              <div className="truncate font-miSansDemiBold450 text-[15px] leading-8 text-[#0F0F0F]">
                {product?.name}
              </div>
            </div>

            {isConfigurableProduct ? (
              <div className="mb-[8px] inline-block rounded-[4px] bg-[#F3F3F4] px-[8px] py-[4px]">
                <div className="truncate font-miSansDemiBold450 text-[12px] leading-[16px] text-[#0F0F0F]">
                  {configurableProductOptionValue?.join('，')}
                </div>
              </div>
            ) : null}

            <div className="flex w-full flex-col">
              <div className={commonStyles.flex_row}>
                <PriceRange priceRange={product?.price_range as PriceRanges} />
                <div className="font-miSansRegular330 text-[14px] text-[#000000]">
                  ×{canReturn ? ordered : qty}
                </div>
              </div>
              {canReturn && (
                <div className="mt-[12px]">
                  <Stepper
                    value={qty}
                    min={1}
                    max={ordered}
                    allowEmpty={true}
                    onChange={(value) => handleQtyChange(id, Number(value))}
                    className="custom-stepper"
                    style={{
                      '--border': '1px solid #f3f3f4',
                      '--border-radius': '999px',
                      '--height': '3rem',
                      '--input-width': '4rem',
                      '--input-background-color': '#fff',
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    [],
  )

  /**
   * 勾选
   */
  const handleCheckChange = useCallback(
    (id: string) => {
      const newData = formatProducts.map((product) => {
        if (product.id === id) {
          return {
            ...product,
            check: !product.check,
          }
        }
        return product
      })
      setFormatProducts(newData)
    },
    [formatProducts],
  )

  /**
   * 修改数量
   */
  const handleQtyChange = useCallback(
    (id: string, value: number) => {
      const newData = formatProducts.map((product) => {
        if (product.id === id) {
          return {
            ...product,
            qty: value,
          }
        }
        return product
      })
      setFormatProducts(newData)
    },
    [formatProducts],
  )

  /**
   * 全选
   */
  const handleAllCheckChange = useCallback(
    (value: boolean) => {
      const newData = formatProducts.map((product) => {
        return {
          ...product,
          check: !value,
        }
      })
      setFormatProducts(newData)
    },
    [formatProducts],
  )

  /**
   * 点击下一步
   */
  const handleNext = useCallback(() => {
    dispatch(setSelectedProducts(checkParams))
    dispatch(setReturnAddressId(addressId))

    openPage({
      route: ROUTE.accountOrderReturn,
      from: from || ROUTE.accountOrderDetail,
      queryParams: {
        orderNumber,
      },
    })

    if (closePopup) {
      closePopup()
    }
  }, [orderNumber, closePopup, checkParams, addressId, openPage, dispatch, from])

  useEffect(() => {
    if (Number(products?.length) > 0) {
      const newProducts = products?.map((product) => ({
        ...product,
        check: false,
        ordered: product?.qty,
        item_id: product?.id,
      }))
      setFormatProducts(newProducts as OrderReturnItem[])
    }
  }, [products])

  return (
    <CustomPopup
      visible={popupVisible}
      showHeader
      headTitle={getI18nString('return_popup_title')}
      style={{
        paddingLeft: 0,
        paddingRight: 0,
      }}
      onClose={() => {
        if (closePopup) {
          closePopup()
        }
      }}>
      <div className="max-h-[600px] flex-1 px-8">
        {sectionListData.map(({ data, canReturn, title, subTitle }, index) => (
          <div key={index}>
            <div className="mb-[24px] mt-[16px] flex flex-row items-baseline">
              <div className={commonStyles.font_16}>{title}</div>
              {subTitle && (
                <div className="ml-[8px] font-miSansRegular330 text-[12px] leading-[14px] text-[#86868B]">
                  {subTitle}
                </div>
              )}
            </div>
            {data.map((item) => (
              <div key={item.id} className="mb-[16px] flex items-start gap-base-12">
                {canReturn && !isOnlyOneProduct && (
                  <div className="mt-[34px]">
                    <CustomCheckbox
                      checked={item?.check}
                      onChange={() => handleCheckChange(item.id)}
                    />
                  </div>
                )}
                {renderReturnItem(canReturn, item, handleQtyChange)}
              </div>
            ))}
          </div>
        ))}
        <div className="flex flex-row items-center justify-between bg-white py-[16px]">
          {!isOnlyOneProduct ? (
            <div className="flex items-start gap-base-12">
              <CustomCheckbox
                checked={isAllChecked}
                onChange={() => handleAllCheckChange(isAllChecked)}>
                <div className="text-[16px] leading-[21px] text-[#0F0F0F]">
                  {getI18nString('checkbox_all')}
                </div>
              </CustomCheckbox>
            </div>
          ) : null}
          <div className={isOnlyOneProduct ? 'flex-1' : ''}>
            <CustomButton
              type="primary"
              customStyle={{
                width: isOnlyOneProduct ? '100%' : 144,
                height: 44,
              }}
              disabled={!checkParams.length}
              onClick={handleNext}>
              <div className="font-miSansDemiBold450 text-[16px] leading-[21px] text-[#FFFFFF]">
                {getI18nString('next')}
              </div>
            </CustomButton>
          </div>
        </div>
      </div>
    </CustomPopup>
  )
}

export default ReturnPopup
